import Database from 'better-sqlite3';
import { eq } from 'drizzle-orm';
import { BetterSQLite3Database, drizzle } from 'drizzle-orm/better-sqlite3';
import { migrate } from 'drizzle-orm/better-sqlite3/migrator';
import { app } from 'electron';
import path from 'path';
import * as schema from '../db/schema';
import { models, models_setting, providers } from '../db/schema';
import { SEED_PROVIDERS } from './config/seedData';

let db: BetterSQLite3Database<typeof schema>;
let dbInstance: Database.Database;

export function initializeDatabase() {
  const dbPath = path.join(app.getPath('userData'), 'liftloom.sqlite');
  dbInstance = new Database(dbPath);
  db = drizzle(dbInstance, { schema });
  return db;
}

export async function runMigrations() {
  if (!db) {
    initializeDatabase();
  }

  migrate(db, {
    migrationsFolder: path.join(__dirname, '../../migrations')
  });
}

export async function execute(_event: any, sql: string, params: any[], method: string) {
  if (!db) {
    initializeDatabase();
  }

  const result = dbInstance.prepare(sql)
  const ret = result[method](...params)
  return toDrizzleResult(ret)

}

function toDrizzleResult(row: Record<string, any>)
function toDrizzleResult(rows: Record<string, any> | Array<Record<string, any>>) {
  if (!rows) {
    return []
  }
  if (Array.isArray(rows)) {
    return rows.map((row) => {
      return Object.keys(row).map((key) => row[key])
    })
  } else {
    return Object.keys(rows).map((key) => rows[key])
  }
}

// 获取当前ISO日期字符串
function getISODate(): string {
  return new Date().toISOString();
}

// 数据库种子数据初始化函数
export async function seedDatabaseIfEmpty(): Promise<void> {
  if (!db) {
    initializeDatabase();
  }

  try {
    // 检查providers表是否有数据
    const existingProviders = await db.select().from(providers).limit(1);

    if (existingProviders.length > 0) {
      console.log('数据库已有数据，跳过种子数据初始化');
      return;
    }

    console.log('开始初始化种子数据...');
    const now = getISODate();

    // 插入种子数据
    let providerIdCounter = Date.now();
    let modelIdCounter = Date.now() + 10000;
    let settingIdCounter = Date.now() + 20000;

    for (const seedProvider of SEED_PROVIDERS) {
      // 插入Provider
      const providerResult = await db.insert(providers).values({
        id: providerIdCounter++, // 生成唯一整数ID
        name: seedProvider.type, // 使用type作为name，与现有逻辑保持一致
        apiKey: '', // 默认为空
        apiHost: seedProvider.apiHost,
        enabled: seedProvider.enabled ? 1 : 0,
        createdAt: now
      }).returning({ id: providers.id });

      const providerId = providerResult[0].id;

      // 插入该Provider的所有模型
      for (const seedModel of seedProvider.models) {
        const modelResult = await db.insert(models).values({
          id: modelIdCounter++, // 生成唯一整数ID
          providerId: providerId,
          name: seedModel.name,
          description: seedModel.description || null,
          inputTokenLimit: seedModel.inputTokenLimit,
          outputTokenLimit: seedModel.outputTokenLimit,
          contextWindow: seedModel.contextWindow,
          inputTokenPrice: seedModel.inputTokenPrice,
          outputTokenPrice: seedModel.outputTokenPrice,
          createdAt: now
        }).returning({ id: models.id });

        const modelId = modelResult[0].id;

        // 为每个模型创建默认设置
        await db.insert(models_setting).values({
          id: settingIdCounter++, // 生成唯一整数ID
          modelId: modelId,
          temperature: 0.7,
          topP: 1.0,
          maxToken: 2048,
          contextCount: 10,
          streamOutput: 1,
          createdAt: now
        });
      }
    }

    console.log('种子数据初始化完成');
  } catch (error) {
    console.error('种子数据初始化失败:', error);
    throw error;
  }
}

// 获取所有Provider及其关联的模型
export async function getProvidersFromDb() {
  if (!db) {
    initializeDatabase();
  }

  try {
    // 获取所有providers
    const allProviders = await db.select().from(providers);

    // 为每个provider获取其关联的models
    const providersWithModels = await Promise.all(
      allProviders.map(async (provider) => {
        const providerModels = await db.select().from(models)
          .where(eq(models.providerId, provider.id));

        return {
          id: provider.id.toString(),
          type: provider.name as any, // 使用name作为type，强制类型转换
          name: provider.name,
          apiKey: provider.apiKey,
          apiHost: provider.apiHost,
          models: providerModels.map(model => ({
            id: model.id.toString(),
            provider: provider.id.toString(),
            name: model.name,
            group: 'default', // 默认分组
            description: model.description || undefined
          })),
          enabled: Boolean(provider.enabled)
        };
      })
    );

    return providersWithModels;
  } catch (error) {
    console.error('获取Provider列表失败:', error);
    return [];
  }
}
