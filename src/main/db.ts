import Database from 'better-sqlite3';
import { eq } from 'drizzle-orm';
import { BetterSQLite3Database, drizzle } from 'drizzle-orm/better-sqlite3';
import { migrate } from 'drizzle-orm/better-sqlite3/migrator';
import { app } from 'electron';
import path from 'path';
import * as schema from '../db/schema';
import { conversations, messages, models, models_setting, providers } from '../db/schema';
import { SEED_PROVIDERS } from './config/seedData';

let db: BetterSQLite3Database<typeof schema>;
let dbInstance: Database.Database;

export function initializeDatabase() {
  const dbPath = path.join(app.getPath('userData'), 'liftloom.sqlite');
  dbInstance = new Database(dbPath);
  db = drizzle(dbInstance, { schema });
  return db;
}

export async function runMigrations() {
  if (!db) {
    initializeDatabase();
  }

  migrate(db, {
    migrationsFolder: path.join(__dirname, '../../migrations')
  });
}

export async function execute(_event: any, sql: string, params: any[], method: string) {
  if (!db) {
    initializeDatabase();
  }

  const result = dbInstance.prepare(sql)
  const ret = result[method](...params)
  return toDrizzleResult(ret)

}

function toDrizzleResult(row: Record<string, any>)
function toDrizzleResult(rows: Record<string, any> | Array<Record<string, any>>) {
  if (!rows) {
    return []
  }
  if (Array.isArray(rows)) {
    return rows.map((row) => {
      return Object.keys(row).map((key) => row[key])
    })
  } else {
    return Object.keys(rows).map((key) => rows[key])
  }
}

// 获取当前ISO日期字符串
function getISODate(): string {
  return new Date().toISOString();
}

// 数据库种子数据初始化函数
export async function seedDatabaseIfEmpty(): Promise<void> {
  if (!db) {
    initializeDatabase();
  }

  try {
    // 检查providers表是否有数据
    const existingProviders = await db.select().from(providers).limit(1);

    if (existingProviders.length > 0) {
      console.log('数据库已有数据，跳过种子数据初始化');
      return;
    }

    console.log('开始初始化种子数据...');
    const now = getISODate();

    // 插入种子数据
    let providerIdCounter = Date.now();
    let modelIdCounter = Date.now() + 10000;
    let settingIdCounter = Date.now() + 20000;

    for (const seedProvider of SEED_PROVIDERS) {
      // 插入Provider
      const providerResult = await db.insert(providers).values({
        id: providerIdCounter++, // 生成唯一整数ID
        name: seedProvider.type, // 使用type作为name，与现有逻辑保持一致
        apiKey: '', // 默认为空
        apiHost: seedProvider.apiHost,
        enabled: seedProvider.enabled ? 1 : 0,
        createdAt: now
      }).returning({ id: providers.id });

      const providerId = providerResult[0].id;

      // 插入该Provider的所有模型
      for (const seedModel of seedProvider.models) {
        const modelResult = await db.insert(models).values({
          id: modelIdCounter++, // 生成唯一整数ID
          providerId: providerId,
          name: seedModel.name,
          description: seedModel.description || null,
          inputTokenLimit: seedModel.inputTokenLimit,
          outputTokenLimit: seedModel.outputTokenLimit,
          contextWindow: seedModel.contextWindow,
          inputTokenPrice: seedModel.inputTokenPrice,
          outputTokenPrice: seedModel.outputTokenPrice,
          createdAt: now
        }).returning({ id: models.id });

        const modelId = modelResult[0].id;

        // 为每个模型创建默认设置
        await db.insert(models_setting).values({
          id: settingIdCounter++, // 生成唯一整数ID
          modelId: modelId,
          temperature: 0.7,
          topP: 1.0,
          maxToken: 2048,
          contextCount: 10,
          streamOutput: 1,
          createdAt: now
        });
      }
    }

    console.log('种子数据初始化完成');
  } catch (error) {
    console.error('种子数据初始化失败:', error);
    throw error;
  }
}

// 获取所有Provider及其关联的模型
export async function getProvidersFromDb() {
  if (!db) {
    initializeDatabase();
  }

  try {
    // 获取所有providers
    const allProviders = await db.select().from(providers);

    // 为每个provider获取其关联的models
    const providersWithModels = await Promise.all(
      allProviders.map(async (provider) => {
        const providerModels = await db.select().from(models)
          .where(eq(models.providerId, provider.id));

        return {
          id: provider.id.toString(),
          type: provider.name as any, // 使用name作为type，强制类型转换
          name: provider.name,
          apiKey: provider.apiKey,
          apiHost: provider.apiHost,
          models: providerModels.map(model => ({
            id: model.id.toString(),
            provider: provider.id.toString(),
            name: model.name,
            group: 'default', // 默认分组
            description: model.description || undefined
          })),
          enabled: Boolean(provider.enabled)
        };
      })
    );

    return providersWithModels;
  } catch (error) {
    console.error('获取Provider列表失败:', error);
    return [];
  }
}

// 获取单个Provider及其关联的模型
export async function getProviderFromDb(id: string) {
  if (!db) {
    initializeDatabase();
  }

  try {
    const numericId = parseInt(id);
    if (isNaN(numericId)) return null;

    const provider = await db.select().from(providers)
      .where(eq(providers.id, numericId))
      .limit(1);

    if (provider.length === 0) return null;

    // 获取该Provider下的所有模型
    const providerModels = await db.select().from(models)
      .where(eq(models.providerId, numericId));

    return {
      id: provider[0].id.toString(),
      type: provider[0].name as any,
      name: provider[0].name,
      apiKey: provider[0].apiKey,
      apiHost: provider[0].apiHost,
      models: providerModels.map(model => ({
        id: model.id.toString(),
        provider: provider[0].id.toString(),
        name: model.name,
        group: 'default',
        description: model.description || undefined
      })),
      enabled: Boolean(provider[0].enabled)
    };
  } catch (error) {
    console.error('获取Provider失败:', error);
    return null;
  }
}

// 保存Provider配置
export async function saveProviderToDb(providerData: any) {
  if (!db) {
    initializeDatabase();
  }

  try {
    const now = getISODate();

    if (providerData.id && providerData.id !== 'new' && !providerData.id.startsWith('new-')) {
      // 更新现有Provider
      const numericId = parseInt(providerData.id);
      if (isNaN(numericId)) throw new Error('无效的Provider ID');

      await db.update(providers)
        .set({
          name: providerData.name || providerData.type,
          apiKey: providerData.apiKey,
          apiHost: providerData.apiHost,
          enabled: providerData.enabled ? 1 : 0
        })
        .where(eq(providers.id, numericId));

      return numericId;
    } else {
      // 创建新Provider
      let providerIdCounter = Date.now();
      const result = await db.insert(providers).values({
        id: providerIdCounter,
        name: providerData.name || providerData.type,
        apiKey: providerData.apiKey,
        apiHost: providerData.apiHost,
        enabled: providerData.enabled ? 1 : 0,
        createdAt: now
      }).returning({ id: providers.id });

      const providerId = result[0].id;

      // 如果有模型数据，保存关联的模型
      if (providerData.models && providerData.models.length > 0) {
        let modelIdCounter = Date.now() + 10000;
        for (const model of providerData.models) {
          await db.insert(models).values({
            id: modelIdCounter++,
            providerId: providerId,
            name: model.name,
            description: model.description || null,
            inputTokenLimit: 4096,
            outputTokenLimit: 4096,
            contextWindow: 8192,
            inputTokenPrice: 0,
            outputTokenPrice: 0,
            createdAt: now
          });
        }
      }

      return providerId;
    }
  } catch (error) {
    console.error('保存Provider失败:', error);
    throw error;
  }
}

// 删除Provider
export async function deleteProviderFromDb(id: string) {
  if (!db) {
    initializeDatabase();
  }

  try {
    const numericId = parseInt(id);
    if (isNaN(numericId)) throw new Error('无效的Provider ID');

    // 首先删除关联的模型设置
    const relatedModels = await db.select().from(models)
      .where(eq(models.providerId, numericId));

    for (const model of relatedModels) {
      await db.delete(models_setting)
        .where(eq(models_setting.modelId, model.id));
    }

    // 删除关联的模型
    await db.delete(models)
      .where(eq(models.providerId, numericId));

    // 最后删除Provider
    await db.delete(providers)
      .where(eq(providers.id, numericId));

    return true;
  } catch (error) {
    console.error('删除Provider失败:', error);
    throw error;
  }
}

// 获取所有模型列表
export async function getAllModelsFromDb() {
  if (!db) {
    initializeDatabase();
  }

  try {
    const result = await db.select().from(models);

    return result.map(item => ({
      id: item.id.toString(),
      provider: item.providerId.toString(),
      name: item.name,
      group: 'default',
      description: item.description || undefined
    }));
  } catch (error) {
    console.error('获取模型列表失败:', error);
    return [];
  }
}

// 获取特定模型的设置
export async function getModelSettingsFromDb(modelId: string) {
  if (!db) {
    initializeDatabase();
  }

  try {
    const numericId = parseInt(modelId);
    if (isNaN(numericId)) return null;

    // 获取模型基本信息
    const modelResult = await db.select().from(models)
      .where(eq(models.id, numericId))
      .limit(1);

    if (modelResult.length === 0) return null;

    // 获取模型设置
    const settingResult = await db.select().from(models_setting)
      .where(eq(models_setting.modelId, numericId))
      .limit(1);

    // 构建Model对象
    const model = {
      id: modelResult[0].id.toString(),
      provider: modelResult[0].providerId.toString(),
      name: modelResult[0].name,
      group: 'default',
      description: modelResult[0].description || undefined,
      // 模型设置
      temperature: settingResult[0]?.temperature || 0.7,
      topP: settingResult[0]?.topP || 1.0,
      maxToken: settingResult[0]?.maxToken || 2048,
      contextCount: settingResult[0]?.contextCount || 10,
      streamOutput: Boolean(settingResult[0]?.streamOutput ?? 1)
    };

    return model;
  } catch (error) {
    console.error(`获取模型设置(ModelID: ${modelId})失败:`, error);
    return null;
  }
}

// 保存模型设置
export async function saveModelSettingsToDb(modelData: any) {
  if (!db) {
    initializeDatabase();
  }

  try {
    const now = getISODate();
    const modelId = parseInt(modelData.id);
    const providerId = parseInt(modelData.provider);

    if (isNaN(modelId) || isNaN(providerId)) {
      throw new Error('无效的模型ID或Provider ID');
    }

    // 检查模型是否存在
    const existingModel = await db.select().from(models)
      .where(eq(models.id, modelId))
      .limit(1);

    if (existingModel.length > 0) {
      // 更新现有模型
      await db.update(models)
        .set({
          name: modelData.name,
          description: modelData.description || null
        })
        .where(eq(models.id, modelId));
    } else {
      // 创建新模型
      await db.insert(models).values({
        id: modelId,
        providerId: providerId,
        name: modelData.name,
        description: modelData.description || null,
        inputTokenLimit: 4096,
        outputTokenLimit: 4096,
        contextWindow: 8192,
        inputTokenPrice: 0,
        outputTokenPrice: 0,
        createdAt: now
      });
    }

    // 检查模型设置是否存在
    const existingSetting = await db.select().from(models_setting)
      .where(eq(models_setting.modelId, modelId))
      .limit(1);

    if (existingSetting.length > 0) {
      // 更新现有设置
      await db.update(models_setting)
        .set({
          temperature: modelData.temperature || 0.7,
          topP: modelData.topP || 1.0,
          maxToken: modelData.maxToken || 2048,
          contextCount: modelData.contextCount || 10,
          streamOutput: modelData.streamOutput ? 1 : 0
        })
        .where(eq(models_setting.modelId, modelId));
    } else {
      // 创建新设置
      let settingIdCounter = Date.now() + 30000;
      await db.insert(models_setting).values({
        id: settingIdCounter,
        modelId: modelId,
        temperature: modelData.temperature || 0.7,
        topP: modelData.topP || 1.0,
        maxToken: modelData.maxToken || 2048,
        contextCount: modelData.contextCount || 10,
        streamOutput: modelData.streamOutput ? 1 : 0,
        createdAt: now
      });
    }

    return true;
  } catch (error) {
    console.error('保存模型设置失败:', error);
    throw error;
  }
}

// 删除模型
export async function deleteModelFromDb(modelId: string) {
  if (!db) {
    initializeDatabase();
  }

  try {
    const numericId = parseInt(modelId);
    if (isNaN(numericId)) throw new Error('无效的模型ID');

    // 首先删除模型设置
    await db.delete(models_setting)
      .where(eq(models_setting.modelId, numericId));

    // 然后删除模型
    await db.delete(models)
      .where(eq(models.id, numericId));

    return true;
  } catch (error) {
    console.error(`删除模型(ID: ${modelId})失败:`, error);
    throw error;
  }
}

// 获取所有对话列表
export async function getConversationsFromDb() {
  if (!db) {
    initializeDatabase();
  }

  try {
    const result = await db.select().from(conversations)
      .orderBy(conversations.createdAt);

    return result.map(item => ({
      id: item.id,
      title: item.title,
      createdAt: item.createdAt
    }));
  } catch (error) {
    console.error('获取对话列表失败:', error);
    return [];
  }
}

// 创建新对话
export async function createConversationInDb(title: string) {
  if (!db) {
    initializeDatabase();
  }

  try {
    const id = Date.now();
    const now = getISODate();

    await db.insert(conversations).values({
      id,
      title,
      createdAt: now
    });

    return id;
  } catch (error) {
    console.error('创建对话失败:', error);
    throw error;
  }
}

// 添加消息
export async function addMessageToDb(conversationId: number, role: string, content: string, modelId: number) {
  if (!db) {
    initializeDatabase();
  }

  try {
    const id = Date.now();
    const now = getISODate();

    await db.insert(messages).values({
      id,
      conversationId,
      role,
      content,
      modelId,
      createdAt: now
    });

    return id;
  } catch (error) {
    console.error('添加消息失败:', error);
    throw error;
  }
}

// 获取对话的所有消息
export async function getMessagesByConversationFromDb(conversationId: number) {
  if (!db) {
    initializeDatabase();
  }

  try {
    const result = await db.select().from(messages)
      .where(eq(messages.conversationId, conversationId))
      .orderBy(messages.createdAt);

    return result.map(item => ({
      id: item.id,
      conversationId: item.conversationId,
      role: item.role,
      content: item.content,
      modelId: item.modelId,
      createdAt: item.createdAt
    }));
  } catch (error) {
    console.error('获取对话消息失败:', error);
    return [];
  }
}
