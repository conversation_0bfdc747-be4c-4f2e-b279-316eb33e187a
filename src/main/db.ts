import { drizzle } from 'drizzle-orm/better-sqlite3';
import Database from 'better-sqlite3';
import { migrate } from 'drizzle-orm/better-sqlite3/migrator';
import { app } from 'electron';
import path from 'path';
import * as schema from '../db/schema';
import { BetterSQLite3Database } from 'drizzle-orm/better-sqlite3';

let db: BetterSQLite3Database<typeof schema>;
let dbInstance: Database.Database;

export function initializeDatabase() {
  const dbPath = path.join(app.getPath('userData'), 'liftloom.sqlite');
  dbInstance = new Database(dbPath);
  db = drizzle(dbInstance, { schema });
  return db;
}

export async function runMigrations() {
  if (!db) {
    initializeDatabase();
  }

  migrate(db, {
    migrationsFolder: path.join(__dirname, '../../migrations')
  });
}

export async function execute(event, sql: string, params: any[], method: string) {
  if (!db) {
    initializeDatabase();
  }

  const result = dbInstance.prepare(sql)
  const ret = result[method](...params)
  return toDrizzleResult(ret)

}

function toDrizzleResult(row: Record<string, any>)
function toDrizzleResult(rows: Record<string, any> | Array<Record<string, any>>) {
  if (!rows) {
    return []
  }
  if (Array.isArray(rows)) {
    return rows.map((row) => {
      return Object.keys(row).map((key) => row[key])
    })
  } else {
    return Object.keys(rows).map((key) => rows[key])
  }
}
