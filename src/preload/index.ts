import { electronAPI } from '@electron-toolkit/preload'
import { contextBridge, ipcRenderer } from 'electron'

// Custom APIs for renderer
const api = {
  // 保留原有的通用数据库执行接口（向后兼容）
  execute: (sql: string, params: any[], method: string) =>
    ipcRenderer.invoke('db:execute', sql, params, method),

  // Provider相关API
  getAllProviders: () => ipcRenderer.invoke('providers:getAll'),
  getProvider: (id: string) => ipcRenderer.invoke('providers:getOne', id),
  saveProvider: (providerData: any) => ipcRenderer.invoke('providers:save', providerData),
  deleteProvider: (id: string) => ipcRenderer.invoke('providers:delete', id),

  // Model相关API
  getAllModels: () => ipcRenderer.invoke('models:getAll'),
  getModelSettings: (modelId: string) => ipcRenderer.invoke('models:getSettings', modelId),
  saveModelSettings: (modelData: any) => ipcRenderer.invoke('models:saveSettings', modelData),
  deleteModel: (modelId: string) => ipcRenderer.invoke('models:delete', modelId),

  // Conversation相关API
  getAllConversations: () => ipcRenderer.invoke('conversations:getAll'),
  createConversation: (title: string) => ipcRenderer.invoke('conversations:create', title),
  addMessage: (conversationId: number, role: string, content: string, modelId: number) =>
    ipcRenderer.invoke('messages:add', conversationId, role, content, modelId),
  getMessagesByConversation: (conversationId: number) =>
    ipcRenderer.invoke('messages:getByConversation', conversationId)
}

// Use `contextBridge` APIs to expose Electron APIs to
// renderer only if context isolation is enabled, otherwise
// just add to the DOM global.
if (process.contextIsolated) {
  try {
    contextBridge.exposeInMainWorld('electron', electronAPI)
    contextBridge.exposeInMainWorld('api', api)
  } catch (error) {
    console.error(error)
  }
} else {
  // @ts-ignore (define in dts)
  window.electron = electronAPI
  // @ts-ignore (define in dts)
  window.api = api
}
