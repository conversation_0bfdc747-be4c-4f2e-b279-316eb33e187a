import { ElectronAPI } from '@electron-toolkit/preload'

// 定义ProviderType（与渲染进程保持一致）
type ProviderType = 'openai' | 'anthropic' | 'gemini' | 'qwenlm' | 'azure-openai' | 'deepseek'

// 定义Provider类型（与渲染进程保持一致）
interface Provider {
  id: string
  type: ProviderType
  name: string
  apiKey: string
  apiHost: string
  models: Array<{
    id: string
    provider: string
    name: string
    group: string
    description?: string
  }>
  enabled: boolean
}

declare global {
  interface Window {
    electron: ElectronAPI
    api: {
      // 保留原有的通用数据库执行接口（向后兼容）
      execute: (sql: string, params: any[], method: string) => Promise<any>

      // 新的面向业务的API
      getAllProviders: () => Promise<Provider[]>
    }
  }
}
