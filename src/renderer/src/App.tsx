import { AppSidebar } from "@renderer/components/share/AppSidebar";
import { ChatArea } from "@renderer/components/share/ChatArea";
import ModelConfigPanel from "@renderer/components/share/ModelConfigPanel";
import { SidebarInset, SidebarProvider } from "@renderer/components/ui/sidebar";
import { useStore } from '@renderer/store/appStore';
import { useState } from 'react';
import { ThemeProvider } from './components/theme-provider';

function App() {

  const appIsModelConfigPanelOpen = useStore((state) => state.isModelConfigPanelOpen);
  const [selectedModelId, setSelectedModelId] = useState(0); // 默认模型ID从配置加载


  return (
    <ThemeProvider defaultTheme="system" storageKey="vite-ui-theme">
      <div className={`h-screen flex flex-col bg-background text-foreground`}>
        <div className="flex-1 flex overflow-hidden">
          <SidebarProvider>
            <AppSidebar />
            <SidebarInset>
              {appIsModelConfigPanelOpen ? (
                <ModelConfigPanel />
              ) : (
                <ChatArea
                  selectedModelId={selectedModelId} // 将选定模型ID传递给ChatArea
                />
              )}
            </SidebarInset>
          </SidebarProvider>
        </div>
      </div>
    </ThemeProvider>
  );
}

export default App;
