import { Conversation } from "@renderer/store/appStore";
import { Loader2, MessageSquare, Trash2 } from "lucide-react";
import { Button } from "../ui/button";
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar
} from "../ui/sidebar";

interface ConversationListProps {
  conversations: Conversation[];
  onSelect: (id: number) => void;
  onClearHistory?: () => void;
  isLoading?: boolean;
}

export function ConversationList({ conversations, onSelect, onClearHistory, isLoading = false }: ConversationListProps) {
  const { state } = useSidebar();
  const isCollapsed = state === "collapsed";

  // 分成"最近"和"一周以前"两类
  const recentConvs = conversations.filter(conv => conv.time === '今天' || conv.time === '昨天');
  const olderConvs = conversations.filter(conv => conv.time !== '今天' && conv.time !== '昨天');

  const handleClearHistory = () => {
    if (onClearHistory) {
      if (window.confirm('确定要清空所有聊天历史记录吗？')) {
        onClearHistory();
      }
    }
  };

  // 加载状态显示
  if (isLoading) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <Loader2 className="h-5 w-5 text-muted-foreground animate-spin" />
      </div>
    );
  }

  // 在折叠状态下只显示一个消息图标
  if (isCollapsed) {
    return (
      <div className="flex-1 overflow-hidden px-1 pt-1 flex flex-col items-center">
        <div className="mt-2">
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8 rounded-md"
            title="聊天历史"
          >
            <MessageSquare className="h-4 w-4 text-muted-foreground" />
          </Button>
        </div>
      </div>
    );
  }

  // 无会话状态
  if (conversations.length === 0) {
    return (
      <div className="flex-1 px-3 py-6 text-center">
        <p className="text-xs text-muted-foreground">还没有任何对话</p>
      </div>
    );
  }

  return (
    <div className="flex-1 overflow-y-auto px-1 pt-1">
      {/* 最近对话 */}
      <SidebarGroup>
        <div className="flex items-center justify-between pr-2">
          <SidebarGroupLabel className="px-2 text-xs text-muted-foreground font-medium uppercase tracking-wide mb-2">
            最近
          </SidebarGroupLabel>
          <Button
            variant="ghost"
            size="icon"
            className="h-5 w-5 rounded-md"
            onClick={handleClearHistory}
            title="清空历史记录"
          >
            <Trash2 className="h-3.5 w-3.5 text-muted-foreground" />
          </Button>
        </div>

        <SidebarMenu>
          {recentConvs.map((conv) => (
            <SidebarMenuItem key={conv.id}>
              <SidebarMenuButton
                isActive={conv.selected}
                onClick={() => onSelect(conv.id)}
                className="w-full justify-start px-2.5 py-2 h-auto rounded-md text-sm"
                tooltip={conv.title}
              >
                <MessageSquare className="w-4 h-4 mr-2.5 opacity-70" />
                <span className="truncate leading-tight font-medium">{conv.title}</span>
              </SidebarMenuButton>
            </SidebarMenuItem>
          ))}
        </SidebarMenu>
      </SidebarGroup>

      {/* 一周以前 */}
      {olderConvs.length > 0 && (
        <SidebarGroup>
          <SidebarGroupLabel className="px-2 text-xs text-muted-foreground font-medium uppercase tracking-wide mb-2 mt-3">
            一周以前
          </SidebarGroupLabel>

          <SidebarMenu>
            {olderConvs.map((conv) => (
              <SidebarMenuItem key={conv.id}>
                <SidebarMenuButton
                  isActive={conv.selected}
                  onClick={() => onSelect(conv.id)}
                  className="w-full justify-start px-2.5 py-2 h-auto rounded-md text-sm"
                  tooltip={conv.title}
                >
                  <MessageSquare className="w-4 h-4 mr-2.5 opacity-70" />
                  <span className="truncate leading-tight font-medium">{conv.title}</span>
                </SidebarMenuButton>
              </SidebarMenuItem>
            ))}
          </SidebarMenu>
        </SidebarGroup>
      )}
    </div>
  );
}
