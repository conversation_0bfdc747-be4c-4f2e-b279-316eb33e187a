import { ConversationList } from "@renderer/components/share/ConversationList";
import { SidebarSearch } from "@renderer/components/share/SidebarSearch";
import { ToolsMenu } from "@renderer/components/share/ToolsMenu";
import { Sidebar, SidebarContent, SidebarHeader, SidebarRail } from "@renderer/components/ui/sidebar";
import { useStore } from "@renderer/store/appStore";
import { useEffect } from "react";

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const conversations = useStore((state) => state.conversations);
  const isLoadingConversations = useStore((state) => state.isLoadingConversations);
  const selectConversation = useStore((state) => state.selectConversation);
  const createNewConversationUI = useStore((state) => state.createNewConversationUI);
  const clearHistory = useStore((state) => state.clearHistory);
  const toggleModelConfigPanel = useStore((state) => state.toggleModelConfigPanel);
  const loadConversations = useStore((state) => state.loadConversations);


  // 初次加载会话列表
  useEffect(() => {
    loadConversations();
  }, [loadConversations]); // 依赖 loadConversations action

  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader>
        <SidebarSearch />
      </SidebarHeader>
      <SidebarContent className="flex flex-col h-full">
        <ConversationList
          conversations={conversations}
          onSelect={selectConversation} // 直接使用 store 的 action
          onClearHistory={clearHistory}   // 直接使用 store 的 action
          isLoading={isLoadingConversations}
        />
        <ToolsMenu
          onNewConversation={createNewConversationUI} // 直接使用 store 的 action
          onOpenModelConfig={toggleModelConfigPanel} // 直接使用 store 的 action
        />
      </SidebarContent>
      <SidebarRail />
    </Sidebar>
  );
}