import { getISODate } from '@renderer/lib/dateUtil';
import { Model, Provider, ProviderType } from "@renderer/types";
import { eq, InferSelectModel } from 'drizzle-orm';
import { models, models_setting, providers } from '../../../db/schema';
import { database } from '../lib/db';

// 数据库模型类型（保留用于其他函数）
type DbProvider = InferSelectModel<typeof providers>;
type DbModel = InferSelectModel<typeof models>;

// 将数据库Provider对象转换为业务Provider对象（保留用于其他函数）
const mapDbProviderToProvider = async (dbProvider: DbProvider): Promise<Provider> => {
  // 获取该Provider下的所有模型
  const dbModels = await database.select().from(models)
    .where(eq(models.providerId, dbProvider.id));

  // 将数据库模型转换为业务模型
  const modelsList = dbModels.map(model => ({
    id: model.id.toString(),
    provider: dbProvider.id.toString(),
    name: model.name,
    group: 'default', // 默认分组
    description: model.description || undefined
  }));

  return {
    id: dbProvider.id.toString(),
    type: dbProvider.name as ProviderType, // 需要确保name与ProviderType兼容
    name: dbProvider.name,
    apiKey: dbProvider.apiKey,
    apiHost: dbProvider.apiHost,
    models: modelsList,
    enabled: Boolean(dbProvider.enabled)
  };
};

// 获取所有已配置的 Provider 列表（包含其下的模型和模型设置）
export const getProviders = async (): Promise<Provider[]> => {
  try {
    // 使用新的preload API直接从主进程获取数据
    return await window.api.getAllProviders();
  } catch (error) {
    console.error('通过IPC获取Provider列表失败:', error);
    return [];
  }
};

// 获取单个 Provider 的详细信息
export const getProvider = async (id: string): Promise<Provider | undefined> => {
  try {
    const numericId = parseInt(id);
    if (isNaN(numericId)) return undefined;

    const result = await database.query.providers.findFirst({
      where: eq(providers.id, numericId)
    });

    if (!result) return undefined;

    // 转换为业务对象并填充models
    return await mapDbProviderToProvider(result);
  } catch (error) {
    console.error(`获取Provider(ID: ${id})失败:`, error);
    return undefined;
  }
};

// 获取所有模型列表
export const getAllModel = async (): Promise<Model[] | undefined> => {
  try {
    const result: DbModel[] = await database.query.models.findMany();

    return result.map(item => ({
      id: item.id.toString(),
      provider: item.providerId.toString(),
      name: item.name,
      group: 'default',
      description: item.description || undefined
    }));
  } catch (error) {
    console.error('获取模型列表失败:', error);
    return [];
  }
}

// 获取特定模型的设置
export const getModelSetting = async (modelId: string): Promise<Model | undefined> => {
  try {
    const numericId = parseInt(modelId);
    if (isNaN(numericId)) return undefined;

    // 获取模型基本信息
    const modelResult = await database.query.models.findFirst({
      where: eq(models.id, numericId)
    });

    if (!modelResult) return undefined;

    // 构建Model对象
    const model: Model = {
      id: modelResult.id.toString(),
      provider: modelResult.providerId.toString(),
      name: modelResult.name,
      group: 'default',
      description: modelResult.description || undefined,
      // 可以根据需要添加从settingResult中获取的其他设置
      // 例如：temperature: settingResult?.temperature
    };

    return model;
  } catch (error) {
    console.error(`获取模型设置(ModelID: ${modelId})失败:`, error);
    return undefined;
  }
};

// 保存 Provider 的配置
export const saveProvider = async (provider: Provider): Promise<void> => {
  try {
    const now = getISODate();

    if (provider.id && provider.id !== 'new') {
      // 更新现有Provider
      const numericId = parseInt(provider.id);
      if (isNaN(numericId)) throw new Error('无效的Provider ID');

      await database.update(providers)
        .set({
          name: provider.name,
          apiKey: provider.apiKey,
          apiHost: provider.apiHost,
          enabled: provider.enabled ? 1 : 0
        })
        .where(eq(providers.id, numericId));
    } else {
      // 创建新Provider
      const id = Date.now();
      await database.insert(providers).values({
        id,
        name: provider.name,
        apiKey: provider.apiKey,
        apiHost: provider.apiHost,
        enabled: provider.enabled ? 1 : 0,
        createdAt: now
      });

      // 如果provider.models不为空，保存关联的模型
      if (provider.models && provider.models.length > 0) {
        for (const model of provider.models) {
          await database.insert(models).values({
            id: parseInt(model.id) || Date.now(),
            providerId: id,
            name: model.name,
            description: model.description || null,
            inputTokenLimit: 4096, // 默认值，可根据实际情况调整
            outputTokenLimit: 4096,
            contextWindow: 8192,
            inputTokenPrice: 0,
            outputTokenPrice: 0,
            createdAt: now
          });
        }
      }
    }
  } catch (error) {
    console.error('保存Provider失败:', error);
    throw error;
  }
};

// 删除 Provider
export const deleteProvider = async (id: string): Promise<void> => {
  try {
    const numericId = parseInt(id);
    if (isNaN(numericId)) throw new Error('无效的Provider ID');

    // 首先删除关联的模型设置
    const relatedModels = await database.select().from(models)
      .where(eq(models.providerId, numericId));

    for (const model of relatedModels) {
      await database.delete(models_setting)
        .where(eq(models_setting.modelId, model.id));
    }

    // 删除关联的模型
    await database.delete(models)
      .where(eq(models.providerId, numericId));

    // 最后删除Provider
    await database.delete(providers)
      .where(eq(providers.id, numericId));
  } catch (error) {
    console.error(`删除Provider(ID: ${id})失败:`, error);
    throw error;
  }
};

// 保存模型设置
export const saveModelSetting = async (model: Model): Promise<void> => {
  try {
    const now = getISODate();
    const modelId = parseInt(model.id);
    const providerId = parseInt(model.provider);

    if (isNaN(modelId) || isNaN(providerId)) {
      throw new Error('无效的模型ID或Provider ID');
    }

    // 检查模型是否存在
    const existingModel = await database.query.models.findFirst({
      where: eq(models.id, modelId)
    });

    if (existingModel) {
      // 更新现有模型
      await database.update(models)
        .set({
          name: model.name,
          description: model.description || null
        })
        .where(eq(models.id, modelId));
    } else {
      // 创建新模型
      await database.insert(models).values({
        id: modelId,
        providerId: providerId,
        name: model.name,
        description: model.description || null,
        inputTokenLimit: 4096, // 默认值，可根据实际情况调整
        outputTokenLimit: 4096,
        contextWindow: 8192,
        inputTokenPrice: 0,
        outputTokenPrice: 0,
        createdAt: now
      });
    }

    // 检查模型设置是否存在
    const existingSetting = await database.query.models_setting.findFirst({
      where: eq(models_setting.modelId, modelId)
    });

    if (existingSetting) {
      // 更新现有设置
      await database.update(models_setting)
        .set({
          // 这里可以添加更多设置字段的更新
          temperature: 0.7, // 默认值，可根据实际情况调整
          topP: 1.0,
          maxToken: 2048,
          streamOutput: 1
        })
        .where(eq(models_setting.modelId, modelId));
    } else {
      // 创建新设置
      await database.insert(models_setting).values({
        id: Date.now(),
        modelId: modelId,
        temperature: 0.7,
        topP: 1.0,
        maxToken: 2048,
        streamOutput: 1,
        createdAt: now
      });
    }
  } catch (error) {
    console.error('保存模型设置失败:', error);
    throw error;
  }
};

// 删除模型
export const deleteModel = async (modelId: string): Promise<void> => {
  try {
    const numericId = parseInt(modelId);
    if (isNaN(numericId)) throw new Error('无效的模型ID');

    // 首先删除模型设置
    await database.delete(models_setting)
      .where(eq(models_setting.modelId, numericId));

    // 然后删除模型
    await database.delete(models)
      .where(eq(models.id, numericId));
  } catch (error) {
    console.error(`删除模型(ID: ${modelId})失败:`, error);
    throw error;
  }
};
