import type { Provider } from '@renderer/types';
import { getAIConfig, getSystemPrompt } from '../config/aiConfig';
import ProviderFactory from '../providers/AiProvider/providerFactory';

// 会话类型定义
export interface ConversationData {
  id: number;
  title: string;
  model: string;
  createdAt: string;
}

// 消息类型定义
export interface MessageData {
  id: number;
  conversationId: number;
  role: string;
  content: string;
  createdAt: string;
}

// 查询所有会话
export const getAllConversations = async () => {
  try {
    // 使用新的preload API直接从主进程获取数据
    return await window.api.getAllConversations();
  } catch (error) {
    console.error('通过IPC获取会话列表失败:', error);
    return [];
  }
};

// 获取会话

// 查询特定会话的所有消息
export const getMessagesByConversationId = async (conversationId: number) => {
  try {
    // 使用新的preload API直接从主进程获取数据
    return await window.api.getMessagesByConversation(conversationId);
  } catch (error) {
    console.error(`通过IPC获取会话ID ${conversationId} 的消息失败:`, error);
    return [];
  }
};

// 创建新会话
export const createConversation = async (title: string) => {
  try {
    const id = Date.now();
    await database.insert(conversations).values({
      id,
      title,
      createdAt: getISODate()
    });
    return id;
  } catch (error) {
    console.error('创建会话失败:', error);
    throw error;
  }
};

// 添加消息
export const addMessage = async (conversationId: number, role: string, content: string, modelId: number) => {
  try {
    const id = Date.now();
    await database.insert(messages).values({
      id,
      conversationId,
      role,
      content,
      modelId,
      createdAt: getISODate()
    });
    return id;
  } catch (error) {
    console.error('添加消息失败:', error);
    throw error;
  }
};

// 删除所有会话和消息
export const clearAllHistory = async () => {
  try {
    await database.delete(messages);
    await database.delete(conversations);
    return true;
  } catch (error) {
    console.error('清空历史记录失败:', error);
    return false;
  }
};

// AI 通信相关的函数简化实现
const createProvider = () => {
  const config = getAIConfig();
  if (!config.apiKey) {
    throw new Error(`请先设置 ${config.provider || 'AI'} API密钥`);
  }

  // 构造 Provider 配置
  const provider: Provider = {
    id: config.provider,
    type: config.provider as any, // 这里需要根据实际支持的类型转换
    name: config.provider,
    apiKey: config.apiKey,
    apiHost: '', // 从配置中获取或使用默认值
    models: [], // 根据需要配置
    enabled: true
  };

  return ProviderFactory.create(provider);
};

const prepareMessages = async (conversationId: number, userMessage: string) => {
  const dbMessages = await getMessagesByConversationId(conversationId);
  return {
    prompt: getSystemPrompt(),
    content: userMessage
  };
};

export const sendMessageToAI = async (conversationId: number, userMessage: string): Promise<string> => {
  try {
    const provider = createProvider();
    const messages = await prepareMessages(conversationId, userMessage);
    return await provider.generateText(messages);
  } catch (error) {
    console.error('AI响应失败:', error);
    throw error;
  }
};

export const streamMessageToAI = async (
  conversationId: number,
  userMessage: string,
  onChunk: (chunk: string) => void
): Promise<string> => {
  try {
    const provider = createProvider();
    const messages = await prepareMessages(conversationId, userMessage);
    const stream = provider.streamText(messages);

    let fullContent = '';
    const reader = stream.getReader();

    while (true) {
      const { done, value } = await reader.read();
      if (done) break;

      fullContent += value;
      onChunk(value);
    }

    return fullContent;
  } catch (error) {
    console.error('AI流式响应失败:', error);
    throw error;
  }
};
