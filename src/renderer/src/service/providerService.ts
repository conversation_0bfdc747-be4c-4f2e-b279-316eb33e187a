import { Provider, Model } from '@renderer/types';
import { database } from '../lib/db';
import { providers, models } from '../../../db/schema';
import { eq } from 'drizzle-orm';
import { getISODate } from '@renderer/lib/dateUtil';

// 获取所有提供商，从providers读取
export const getAllProviders = async (): Promise<Provider[]> => {
  try {
    const result = await database.select().from(providers);
    return result.map(item => ({
      id: item.id.toString(),
      type: item.name as any, // 需要转换为ProviderType
      name: item.name,
      apiKey: item.apiKey,
      apiHost: item.apiHost,
      models: [], // 稍后填充
      enabled: Boolean(item.enabled)
    }));
  } catch (error) {
    console.error('获取提供商失败:', error);
    throw error;
  }
};

// 获取提供商的所有模型
export const getModelsByProviderId = async (providerId: number): Promise<Model[]> => {
  try {
    const result = await database.select().from(models).where(eq(models.providerId, providerId));
    return result.map(item => ({
      id: item.id.toString(),
      provider: providerId.toString(),
      name: item.name,
      group: 'default',
      description: item.description || undefined
    }));
  } catch (error) {
    console.error('获取模型失败:', error);
    throw error;
  }
};

// 添加新提供商
export const addProvider = async (provider: Omit<Provider, 'id' | 'models'>): Promise<number> => {
  try {
    const id = Date.now();
    await database.insert(providers).values({
      id,
      name: provider.name,
      apiKey: provider.apiKey,
      apiHost: provider.apiHost,
      enabled: provider.enabled ? 1 : 0,
      createdAt: getISODate()
    });
    return id;
  } catch (error) {
    console.error('添加提供商失败:', error);
    throw error;
  }
};
