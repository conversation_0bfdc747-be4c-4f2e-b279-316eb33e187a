import { create } from 'zustand';
import { Provider, ProviderType } from '@renderer/types';
import { getProviders, saveProvider, deleteProvider } from '@renderer/service/configService';
import { SYSTEM_MODELS } from '@renderer/config/model';
import { PROVIDER_NAMES, DEFAULT_API_HOSTS } from '@renderer/config/providers';

// 连接测试状态
export type ConnectionStatus = 'idle' | 'testing' | 'success' | 'error';

// Provider配置状态接口
interface ProviderConfigState {
  providers: Provider[];
  isLoading: boolean;
  connectionStatuses: Record<string, ConnectionStatus>;
  errorMessages: Record<string, string>;
}

// Provider配置操作接口
interface ProviderConfigActions {
  loadProviders: () => Promise<void>;
  updateProvider: (provider: Provider) => Promise<void>;
  deleteProviderConfig: (providerId: string) => Promise<void>;
  testConnection: (provider: Provider) => Promise<void>;
  setConnectionStatus: (providerId: string, status: ConnectionStatus, error?: string) => void;
  resetConnectionStatus: (providerId: string) => void;
}

// Provider配置Store类型
type ProviderConfigStore = ProviderConfigState & ProviderConfigActions;

// 创建Provider配置Store
export const useProviderStore = create<ProviderConfigStore>((set, get) => ({
  // 初始状态
  providers: [],
  isLoading: false,
  connectionStatuses: {},
  errorMessages: {},

  // 加载所有供应商配置
  loadProviders: async () => {
    set({ isLoading: true });
    try {
      const providersData = await getProviders();

      // 确保所有支持的供应商类型都存在
      const existingTypes = providersData.map(p => p.type);
      const allProviderTypes: ProviderType[] = ['openai', 'anthropic', 'gemini', 'deepseek', 'qwenlm', 'azure-openai'];

      // 为不存在的供应商类型创建默认配置
      const missingProviders = allProviderTypes
        .filter(type => !existingTypes.includes(type))
        .map(type => ({
          id: `new-${type}`,
          type,
          name: PROVIDER_NAMES[type],
          apiKey: '',
          apiHost: DEFAULT_API_HOSTS[type],
          models: SYSTEM_MODELS[type] || [],
          enabled: false
        }));

      set({
        providers: [...providersData, ...missingProviders],
        isLoading: false
      });
    } catch (error) {
      console.error('加载供应商配置失败:', error);
      set({ isLoading: false });
    }
  },

  // 更新供应商配置
  updateProvider: async (updatedProvider: Provider) => {
    try {
      await saveProvider(updatedProvider);
      // 重新加载以获取最新数据
      await get().loadProviders();
    } catch (error) {
      console.error('更新供应商配置失败:', error);
      throw error;
    }
  },

  // 删除供应商配置
  deleteProviderConfig: async (providerId: string) => {
    try {
      await deleteProvider(providerId);
      await get().loadProviders();
    } catch (error) {
      console.error('删除供应商配置失败:', error);
      throw error;
    }
  },

  // 测试连接
  testConnection: async (provider: Provider) => {
    const { setConnectionStatus } = get();

    try {
      setConnectionStatus(provider.id, 'testing');

      // 动态导入ProviderFactory以避免循环依赖
      const { default: ProviderFactory } = await import('@renderer/providers/AiProvider/providerFactory');

      // 创建临时Provider对象用于测试
      const testProvider: Provider = {
        ...provider,
        enabled: true
      };

      // 使用ProviderFactory创建Provider实例
      const providerInstance = ProviderFactory.create(testProvider);

      // 尝试调用简单API进行验证
      await providerInstance.generateText({
        prompt: "System prompt for testing",
        content: "Hello, this is a connection test."
      });

      setConnectionStatus(provider.id, 'success');
    } catch (error) {
      console.error('连接测试失败:', error);
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      setConnectionStatus(provider.id, 'error', errorMessage);
    }
  },

  // 设置连接状态
  setConnectionStatus: (providerId: string, status: ConnectionStatus, error?: string) => {
    set(state => ({
      connectionStatuses: {
        ...state.connectionStatuses,
        [providerId]: status
      },
      errorMessages: {
        ...state.errorMessages,
        [providerId]: error || ''
      }
    }));
  },

  // 重置连接状态
  resetConnectionStatus: (providerId: string) => {
    set(state => ({
      connectionStatuses: {
        ...state.connectionStatuses,
        [providerId]: 'idle'
      },
      errorMessages: {
        ...state.errorMessages,
        [providerId]: ''
      }
    }));
  }
}));
