/// <reference types="vite/client" />

import { Provider } from './types'

interface Window {
  api: {
    // 保留原有的通用数据库执行接口（向后兼容）
    execute: (sql: string, params: any[], method: string) => Promise<any>

    // Provider相关API
    getAllProviders: () => Promise<Provider[]>
    getProvider: (id: string) => Promise<Provider | null>
    saveProvider: (providerData: any) => Promise<number>
    deleteProvider: (id: string) => Promise<boolean>

    // Model相关API
    getAllModels: () => Promise<Array<{
      id: string
      provider: string
      name: string
      group: string
      description?: string
    }>>
    getModelSettings: (modelId: string) => Promise<any | null>
    saveModelSettings: (modelData: any) => Promise<boolean>
    deleteModel: (modelId: string) => Promise<boolean>

    // Conversation相关API
    getAllConversations: () => Promise<Array<{
      id: number
      title: string
      createdAt: string
    }>>
    createConversation: (title: string) => Promise<number>
    addMessage: (conversationId: number, role: string, content: string, modelId: number) => Promise<number>
    getMessagesByConversation: (conversationId: number) => Promise<Array<{
      id: number
      conversationId: number
      role: string
      content: string
      modelId: number
      createdAt: string
    }>>
  }
}
