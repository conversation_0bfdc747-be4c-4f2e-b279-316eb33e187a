{"name": "liftloom", "version": "1.0.0", "description": "An Electron application with React and TypeScript", "main": "./out/main/index.js", "author": "example.com", "homepage": "https://electron-vite.org", "scripts": {"format": "prettier --write .", "lint": "eslint --cache .", "typecheck:node": "tsc --noEmit -p tsconfig.node.json --composite false", "typecheck:web": "tsc --noEmit -p tsconfig.web.json --composite false", "typecheck": "npm run typecheck:node && npm run typecheck:web", "start": "electron-vite preview", "dev": "electron-vite dev", "build": "npm run typecheck && electron-vite build", "postinstall": "electron-builder install-app-deps", "build:unpack": "npm run build && electron-builder --dir", "build:win": "npm run build && electron-builder --win", "build:mac": "electron-vite build && electron-builder --mac", "build:linux": "electron-vite build && electron-builder --linux", "db:generate": "drizzle-kit generate"}, "dependencies": {"@ai-sdk/deepseek": "^0.2.13", "@ai-sdk/google": "^1.2.18", "@ai-sdk/openai": "^1.3.6", "@electron-toolkit/preload": "^3.0.1", "@electron-toolkit/utils": "^4.0.0", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-menubar": "^1.1.14", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-tooltip": "^1.1.8", "@tailwindcss/vite": "^4.0.13", "ai": "^4.2.10", "better-sqlite3": "^11.9.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^16.4.7", "drizzle-kit": "^0.30.6", "drizzle-orm": "^0.41.0", "electron-updater": "^6.3.9", "lucide-react": "^0.479.0", "openai": "^4.91.0", "tailwind-merge": "^3.0.2", "tailwindcss": "^4.0.13", "zod": "^3.24.2", "zustand": "^5.0.5"}, "devDependencies": {"@electron-toolkit/eslint-config-prettier": "^3.0.0", "@electron-toolkit/eslint-config-ts": "^3.0.0", "@electron-toolkit/tsconfig": "^1.0.1", "@types/node": "^22.13.4", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@vitejs/plugin-react": "^4.3.4", "electron": "^34.2.0", "electron-builder": "^25.1.8", "electron-vite": "^3.0.0", "eslint": "^9.20.1", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.19", "prettier": "^3.5.1", "react": "^18.3.1", "react-dom": "^18.3.1", "tsx": "^4.19.3", "tw-animate-css": "^1.3.0", "typescript": "^5.7.3", "vite": "^6.1.0"}}